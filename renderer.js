const { ipcRenderer } = require('electron')

console.log('🔴🔴🔴 renderer.js开始执行 - 版本2025-09-01-12:48');
console.error('🔴🔴🔴 renderer.js开始执行 - 版本2025-09-01-12:48');

// 添加全局键盘事件监听器来测试
document.addEventListener('keydown', (e) => {
  console.log('🟡🟡🟡 全局键盘事件:', e.key, '目标元素:', e.target.tagName, e.target.id);
  if (e.key === 'Enter') {
    console.log('🟡🟡🟡 全局Enter键被按下！目标:', e.target);
  }
});

let webview = document.getElementById('webview')
console.log('🔴🔴🔴 webview元素:', webview);

// URL history management
const MAX_URL_HISTORY = 100; // Maximum number of URLs to store
const MAX_BOOKMARKS = 1000; // Maximum number of bookmarks to store

// Add zoom constants at the beginning of file
const MIN_SCALE = 0.5;
const MAX_SCALE = 3.0;

// Progress bar control
let progressBar = document.getElementById('progress-bar');
let progressTimer = null;

// Add a variable to record user input URL
let userInputUrl = null;

webview.addEventListener('did-start-loading', () => {
  console.log('Loading started, URL:', webview.getURL());
  isNavigating = true;
  navigationStartTime = Date.now();
  // currentLoadingUrl 现在在 will-navigate 事件中设置，更加准确



  // Reset progress
  if (progressTimer) clearTimeout(progressTimer);
  progressBar.style.width = '0%';

  // Simulate progress until page loads
  let progress = 0;
  progressTimer = setInterval(() => {
    progress += Math.random() * 10;
    if (progress > 90) {
      clearInterval(progressTimer);
      return;
    }
    progressBar.style.width = progress + '%';
  }, 500);
});

// Comprehensive did-stop-loading handler - consolidates all loading completion logic
webview.addEventListener('did-stop-loading', () => {
  const currentUrl = webview.getURL();
  console.log('did-stop-loading: 加载完成，当前URL:', currentUrl, '用户输入URL:', userInputUrl);
  
  // 1. Complete and hide progress bar
  if (progressTimer) clearTimeout(progressTimer);
  progressBar.style.width = '100%';
  setTimeout(() => {
    progressBar.style.width = '0%';
  }, 500);
  
  // 2. Reset navigation state
  isNavigating = false;
  currentLoadingUrl = null;
  clearLoadingTimeout();
  
  // 3. Update URL input boxes with final URL
  const dragArea = document.getElementById('drag-area');
  const urlInput = dragArea?.querySelector('input');
  if (dragArea && dragArea.style.display === 'flex' && urlInput) {
    urlInput.value = currentUrl;
    console.log('did-stop-loading: 立即更新URL输入框为最终URL:', currentUrl);
  }
  
  // 4. Update modal URL input if modal is open
  const urlModal = document.getElementById('url-modal');
  const modalUrlInput = document.getElementById('url-input');
  if (urlModal && urlModal.style.display === 'block' && modalUrlInput) {
    modalUrlInput.value = currentUrl;
  }
  
  // 5. Loading completed
  console.log('did-stop-loading: 加载完成');
});

// Add top area right-click handler
document.addEventListener('DOMContentLoaded', () => {
  // Create drag area with URL input
  const dragArea = document.createElement('div');
  dragArea.id = 'drag-area';
  dragArea.style.position = 'fixed';
  dragArea.style.top = '0';
  dragArea.style.left = '0';
  dragArea.style.right = '0';
  dragArea.style.height = '30px';
  dragArea.style.zIndex = '9999';
  dragArea.style.webkitAppRegion = 'no-drag';
  dragArea.style.display = 'flex'; // 始终显示
  // 玻璃透明效果
  dragArea.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
  dragArea.style.backdropFilter = 'blur(20px) saturate(180%)';
  dragArea.style.webkitBackdropFilter = 'blur(20px) saturate(180%)';
  dragArea.style.border = '1px solid rgba(255, 255, 255, 0.2)';
  dragArea.style.borderTop = 'none';
  dragArea.style.borderLeft = 'none';
  dragArea.style.borderRight = 'none';
  dragArea.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.37)';
  dragArea.style.alignItems = 'center';
  dragArea.style.justifyContent = 'center';
  dragArea.style.padding = '0 80px';
  dragArea.style.transition = 'all 0.3s ease';

  // Create suggestions container
  const suggestionsContainer = document.createElement('div');
  suggestionsContainer.style.position = 'fixed';
  suggestionsContainer.style.top = '32px';
  suggestionsContainer.style.left = '50%';
  suggestionsContainer.style.transform = 'translateX(-50%)';
  suggestionsContainer.style.width = '50%';
  suggestionsContainer.style.maxWidth = '600px';
  suggestionsContainer.style.maxHeight = '240px';
  suggestionsContainer.style.overflowY = 'auto';
  suggestionsContainer.style.overflowX = 'hidden';
  // 现代化玻璃效果建议容器
  suggestionsContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.25)';
  suggestionsContainer.style.backdropFilter = 'blur(25px) saturate(180%)';
  suggestionsContainer.style.webkitBackdropFilter = 'blur(25px) saturate(180%)';
  suggestionsContainer.style.border = '1px solid rgba(255, 255, 255, 0.4)';
  suggestionsContainer.style.borderRadius = '12px';
  suggestionsContainer.style.boxShadow = '0 8px 32px 0 rgba(31, 38, 135, 0.4), 0 2px 8px 0 rgba(0, 0, 0, 0.1)';
  suggestionsContainer.style.display = 'none';
  suggestionsContainer.style.zIndex = '10000';
  suggestionsContainer.style.padding = '4px 0';
  
  // 添加滚动条样式
  suggestionsContainer.style.scrollbarWidth = 'thin';
  suggestionsContainer.style.scrollbarColor = 'rgba(255, 255, 255, 0.3) transparent';
  
  // 为Webkit浏览器添加自定义滚动条样式
  const style = document.createElement('style');
  style.textContent = `
    #drag-area + div::-webkit-scrollbar {
      width: 6px;
    }
    #drag-area + div::-webkit-scrollbar-track {
      background: transparent;
    }
    #drag-area + div::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }
    #drag-area + div::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  `;
  document.head.appendChild(style);

  // Create URL input
  const urlInput = document.createElement('input');
  urlInput.type = 'text';
  urlInput.style.width = '50%';
  urlInput.style.maxWidth = '600px';
  urlInput.style.margin = '0 auto';
  urlInput.style.padding = '6px 12px';
  urlInput.style.border = '1px solid rgba(255, 255, 255, 0.3)';
  urlInput.style.borderRadius = '9px';
  urlInput.style.outline = 'none';
  urlInput.style.webkitAppRegion = 'no-drag';
  urlInput.style.display = 'block'; // 始终显示
  // 玻璃效果输入框
  urlInput.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
  urlInput.style.backdropFilter = 'blur(10px)';
  urlInput.style.webkitBackdropFilter = 'blur(10px)';
  urlInput.style.fontSize = '13px';
  urlInput.style.color = '#333';
  urlInput.style.transition = 'all 0.2s ease';
  urlInput.style.boxShadow = '0 2px 8px 0 rgba(31, 38, 135, 0.2)';
  urlInput.placeholder = '输入URL或搜索内容';

  // Create refresh button
  const refreshButton = document.createElement('button');
  refreshButton.style.position = 'absolute';
  refreshButton.style.right = '15px';
  refreshButton.style.top = '50%';
  refreshButton.style.transform = 'translateY(-50%)';
  refreshButton.style.border = 'none';
  refreshButton.style.background = 'none';
  refreshButton.style.padding = '6px';
  refreshButton.style.cursor = 'pointer';
  refreshButton.style.display = 'none';
  refreshButton.style.webkitAppRegion = 'no-drag';
  refreshButton.style.opacity = '0.6';
  refreshButton.style.color = '#666';
  refreshButton.innerHTML = `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.3"/>
  </svg>`;

  // Add hover effect
  refreshButton.addEventListener('mouseover', () => {
    refreshButton.style.opacity = '1';
    refreshButton.style.color = '#333';
  });

  refreshButton.addEventListener('mouseout', () => {
    refreshButton.style.opacity = '0.6';
    refreshButton.style.color = '#666';
  });

  // Add click handler for refresh
  refreshButton.addEventListener('click', () => {
    // 如果当前正在加载，先停止加载
    if (isNavigating) {
      console.log('Stopping current navigation before reloading');
      webview.stop();
      isNavigating = false;
      currentLoadingUrl = null;
      clearLoadingTimeout();
    }

    // 重置进度条
    const progressBar = document.getElementById('progress-bar');
    if (progressBar) {
      progressBar.style.width = '0%';
    }

    // 重新加载页面
    webview.reload();
    isNavigating = true;
    currentLoadingUrl = webview.getURL();
  });

  let selectedIndex = -1;
  let suggestions = [];
  
  // 将这些变量设为全局，以便删除函数可以访问
  window.dragAreaSuggestions = suggestions;
  window.dragAreaSelectedIndex = selectedIndex;
  window.dragAreaUpdateSelection = null; // 稍后会设置

  // Add input handler for suggestions
  urlInput.addEventListener('input', () => {
    // 重置选中状态
    selectedIndex = -1;
    window.dragAreaSelectedIndex = selectedIndex;
    
    suggestions = getUrlSuggestions(urlInput.value);
    window.dragAreaSuggestions = suggestions;
    suggestionsContainer.innerHTML = suggestions
      .map((url, index) => `
        <div class="url-suggestion"
             style="
               padding: 12px 16px; 
               cursor: pointer; 
               color: #333; 
               font-size: 13px;
               border-radius: 6px;
               margin: 4px 8px;
               transition: all 0.2s ease;
               border-left: 3px solid transparent;
               background: rgba(255, 255, 255, 0.1);
               display: flex;
               align-items: center;
               justify-content: space-between;
             "
             data-index="${index}">
          <div style="
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex: 1;
          ">${url}</div>
          <button class="delete-history-btn" 
                  data-url="${url.replace(/"/g, '&quot;')}"
                  style="
                    margin-left: 8px;
                    padding: 4px 6px;
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    border-radius: 4px;
                    color: #666;
                    cursor: pointer;
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    flex-shrink: 0;
                  "
                  onmouseover="this.style.background='rgba(255, 0, 0, 0.2)'; this.style.color='#d32f2f'; this.style.opacity='1';"
                  onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.color='#666'; this.style.opacity='0.7';"
                  onclick="event.stopPropagation(); removeDragAreaHistoryItem('${url.replace(/'/g, "\\'")}');">×</button>
        </div>
      `)
      .join('');

    suggestionsContainer.style.display = suggestions.length ? 'block' : 'none';

    // Add click handlers for suggestions
    suggestionsContainer.querySelectorAll('.url-suggestion').forEach(suggestion => {
      suggestion.addEventListener('click', () => {
        const url = suggestions[suggestion.dataset.index];
        
        // 立即显示点击的建议URL
        console.log('点击建议: 立即显示URL:', url);
        urlInput.value = url;

        // 设置用户输入标记
        window.userInputtedUrl = url;
        window.userInputTime = Date.now();

        // 开始导航
        loadURL(url);
        addToUrlHistory(url);
        hideSuggestions();
        urlInput.blur();
      });

      suggestion.addEventListener('mouseover', () => {
        selectedIndex = parseInt(suggestion.dataset.index);
        window.dragAreaSelectedIndex = selectedIndex;
        updateSelection();
      });

      // 添加更精细的悬停效果
      suggestion.addEventListener('mouseenter', () => {
        if (selectedIndex !== parseInt(suggestion.dataset.index)) {
          suggestion.style.background = 'rgba(255, 255, 255, 0.2)';
          suggestion.style.transform = 'translateX(1px)';
        }
      });

      suggestion.addEventListener('mouseleave', () => {
        if (selectedIndex !== parseInt(suggestion.dataset.index)) {
          suggestion.style.background = 'rgba(255, 255, 255, 0.1)';
          suggestion.style.transform = 'translateX(0)';
        }
      });
    });

    if (suggestions.length) {
      showSuggestions();
    } else {
      hideSuggestions();
    }
  });

  function updateSelection() {
    const items = suggestionsContainer.querySelectorAll('.url-suggestion');
    const currentSelectedIndex = window.dragAreaSelectedIndex || selectedIndex;
    items.forEach((item, index) => {
      if (index === currentSelectedIndex) {
        item.style.background = 'linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.4))';
        item.style.borderLeftColor = 'rgba(0, 122, 255, 0.8)';
        item.style.boxShadow = '0 2px 8px rgba(0, 122, 255, 0.2)';
        item.style.transform = 'translateX(2px)';
      } else {
        item.style.background = 'rgba(255, 255, 255, 0.1)';
        item.style.borderLeftColor = 'transparent';
        item.style.boxShadow = 'none';
        item.style.transform = 'translateX(0)';
      }
    });
    if (currentSelectedIndex >= 0 && currentSelectedIndex < items.length) {
      items[currentSelectedIndex].scrollIntoView({ block: 'nearest' });
    }
  }
  
  // 设置全局访问
  window.dragAreaUpdateSelection = updateSelection;

  function hideDragArea() {
    dragArea.style.display = 'none';
    urlInput.style.display = 'none';
    refreshButton.style.display = 'none';
    suggestionsContainer.style.display = 'none';
    ipcRenderer.send('toggle-traffic-lights');
  }

  function showDragArea() {
    // Send message to main process to toggle traffic lights
    ipcRenderer.send('toggle-traffic-lights');
  }

  // Add URL input handler with capture phase to ensure it runs first
  urlInput.addEventListener('keydown', (e) => {
    console.log('🔥🔥🔥 dragarea URL输入框键盘事件:', e.key, 'dragarea显示状态:', dragArea.style.display);
    if (e.key === 'Enter') {
      console.log('🔥🔥🔥 dragarea Enter键被按下！dragarea显示状态:', dragArea.style.display);
      e.preventDefault(); // 阻止默认行为
      e.stopPropagation(); // 阻止事件冒泡
      const url = urlInput.value.trim();
      if (url) {
        // 确定最终要导航的URL - 确保使用当前输入框的值
        let targetUrl;
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          targetUrl = suggestions[selectedIndex];
        } else {
          targetUrl = url;
        }
        
        console.log('!!!!! Enter键按下，立即显示用户输入:', targetUrl);
        console.log('!!!!! selectedIndex:', selectedIndex, 'suggestions.length:', suggestions.length);
        
        // ========== 立即显示用户输入的URL并强制保持 ==========
        urlInput.value = targetUrl;
        console.log('🔥 Enter键: 立即显示URL:', targetUrl);

        // 重置选中状态
        selectedIndex = -1;
        window.dragAreaSelectedIndex = selectedIndex;

        // 隐藏建议
        hideSuggestions();

        // 失焦
        urlInput.blur();

        // 立即开始导航
        console.log('🔥 Enter键: 开始导航到:', targetUrl);
        loadURL(targetUrl);
        addToUrlHistory(targetUrl);

        // 强制保持URL显示 - 使用定时器反复设置URL值
        let forceDisplayCount = 0;
        const forceDisplayInterval = setInterval(() => {
          if (forceDisplayCount < 20) { // 持续1秒 (20 * 50ms)
            urlInput.value = targetUrl;
            forceDisplayCount++;
          } else {
            clearInterval(forceDisplayInterval);
            console.log('🔥 停止强制显示URL');
          }
        }, 50);
      }
    } else if (e.key === 'Escape') {
      console.log('ESC键: 停止加载');

      // 停止加载
      if (isNavigating) {
        webview.stop();
        isNavigating = false;
        currentLoadingUrl = null;
        clearLoadingTimeout();

        // 清除进度条
        const progressBar = document.getElementById('progress-bar');
        if (progressBar) {
          progressBar.style.width = '0%';
        }
        console.log('ESC键: 已停止加载并清除进度条');
      }

      // 隐藏建议列表
      hideSuggestions();

      // 重置选中状态
      selectedIndex = -1;
      window.dragAreaSelectedIndex = selectedIndex;

      // 失焦但保持当前URL显示（不改变URL）
      urlInput.blur();
      console.log('ESC键: 保持当前URL显示:', urlInput.value);
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (suggestions.length > 0) {
        selectedIndex = (selectedIndex + 1) % suggestions.length;
        window.dragAreaSelectedIndex = selectedIndex;
        updateSelection();
        // 确保索引有效后再设置值
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          urlInput.value = suggestions[selectedIndex];
        }
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (suggestions.length > 0) {
        selectedIndex = selectedIndex <= 0 ? suggestions.length - 1 : selectedIndex - 1;
        window.dragAreaSelectedIndex = selectedIndex;
        updateSelection();
        // 确保索引有效后再设置值
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          urlInput.value = suggestions[selectedIndex];
        }
      }
    }
    e.stopPropagation();
  });

  // Track focus state to handle click selection properly
  let urlInputWasFocused = false;

  // Prevent drag when clicking in input and track focus state
  urlInput.addEventListener('mousedown', (e) => {
    e.stopPropagation();
    // 记录点击时的焦点状态
    urlInputWasFocused = (document.activeElement === urlInput);
  });

  // select all text when click in input
  urlInput.addEventListener('click', () => {
    // 只有当点击前urlinput不在焦点时，点击后才全选
    if (!urlInputWasFocused) {
      setTimeout(() => {
        urlInput.select();
      }, 0);
    }
  });

  // 修改鼠标悬停效果 - 玻璃风格
  urlInput.addEventListener('mouseover', () => {
      urlInput.style.backgroundColor = 'rgba(255, 255, 255, 0.35)';
      urlInput.style.backdropFilter = 'blur(15px)';
      urlInput.style.webkitBackdropFilter = 'blur(15px)';
      urlInput.style.boxShadow = '0 4px 12px 0 rgba(31, 38, 135, 0.3)';
  });

  urlInput.addEventListener('mouseout', () => {
    if (document.activeElement !== urlInput) {
      urlInput.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
      urlInput.style.backdropFilter = 'blur(10px)';
      urlInput.style.webkitBackdropFilter = 'blur(10px)';
      urlInput.style.boxShadow = '0 2px 8px 0 rgba(31, 38, 135, 0.2)';
    }
  });

  urlInput.addEventListener('focus', () => {
    isEditingUrlInput = true;
    urlInput.select();
    urlInput.style.backgroundColor = 'rgba(255, 255, 255, 0.4)';
    urlInput.style.backdropFilter = 'blur(15px)';
    urlInput.style.webkitBackdropFilter = 'blur(15px)';
    urlInput.style.boxShadow = '0 4px 12px 0 rgba(31, 38, 135, 0.3)';
    urlInput.style.border = '1px solid rgba(255, 255, 255, 0.5)';
  });

  urlInput.addEventListener('blur', () => {
    isEditingUrlInput = false;
    
    // 如果用户刚输入了URL，保持显示用户输入的内容，不要立即覆盖
    if (userInputUrl) {
      console.log('blur事件: 保持显示用户输入的URL:', userInputUrl);
      urlInput.value = userInputUrl;
    } else {
      // 只有在没有用户输入时，才显示当前页面URL
      urlInput.value = webview.getURL();
      console.log('blur事件: 显示当前页面完整URL:', webview.getURL());
    }
    
    urlInput.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
    urlInput.style.backdropFilter = 'blur(10px)';
    urlInput.style.webkitBackdropFilter = 'blur(10px)';
    urlInput.style.boxShadow = '0 2px 8px 0 rgba(31, 38, 135, 0.2)';
    urlInput.style.border = '1px solid rgba(255, 255, 255, 0.3)';
  });

  // Add components to document
  dragArea.appendChild(urlInput);
  dragArea.appendChild(refreshButton);
  document.body.appendChild(dragArea);
  document.body.appendChild(suggestionsContainer);

  // 挂到window，供全局事件使用
  window.suggestionsContainer = suggestionsContainer;
  window.urlInput = urlInput;

  console.log('🚀🚀🚀 dragarea已创建并添加到DOM，URL输入框已设置事件监听器');
  console.log('🚀🚀🚀 dragarea显示状态:', dragArea.style.display);
  console.log('🚀🚀🚀 URL输入框元素:', urlInput);

  // 测试URL输入框是否能正常工作
  setTimeout(() => {
    console.log('🔵🔵🔵 5秒后检查dragarea状态');
    console.log('🔵🔵🔵 dragarea在DOM中?', document.body.contains(dragArea));
    console.log('🔵🔵🔵 dragarea显示状态:', dragArea.style.display);
    console.log('🔵🔵🔵 URL输入框在DOM中?', document.body.contains(urlInput));
    console.log('🔵🔵🔵 URL输入框可见?', urlInput.style.display);

    // 手动测试事件
    urlInput.addEventListener('click', () => {
      console.log('🟢🟢🟢 URL输入框被点击了！');
    });

    urlInput.addEventListener('focus', () => {
      console.log('🟢🟢🟢 URL输入框获得焦点！');
    });

    urlInput.addEventListener('input', () => {
      console.log('🟢🟢🟢 URL输入框内容改变！当前值:', urlInput.value);
    });
  }, 5000);

  // Handle titlebar visibility toggle with all functionality
  ipcRenderer.on('toggle-titlebar', async (e, isVisible) => {
    console.log('🎯🎯🎯 toggle-titlebar事件触发，isVisible:', isVisible);
    const content = document.querySelector('.content');
    const progressBar = document.getElementById('progress-bar');

    if (isVisible) {
      content.classList.add('shifted');
      progressBar.classList.add('shifted');
    } else {
      content.classList.remove('shifted');
      progressBar.classList.remove('shifted');
    }

    // 设置拖动区域状态
    dragArea.style.webkitAppRegion = isVisible ? 'drag' : 'no-drag';
    dragArea.style.display = isVisible ? 'flex' : 'none';
    urlInput.style.display = isVisible ? 'block' : 'none';
    refreshButton.style.display = isVisible ? 'block' : 'none';
    suggestionsContainer.style.display = 'none';

    console.log('🎯🎯🎯 dragarea显示状态已设置为:', dragArea.style.display);
    console.log('🎯🎯🎯 URL输入框显示状态已设置为:', urlInput.style.display);

    if (isVisible) {
      // 异步获取和更新颜色
      try {
        const color = await getFaviconColor();
        // 只有当颜色不是浅色时才更新为带色调的玻璃效果
        if (!(color.r > 240 && color.g > 240 && color.b > 240)) {
          dragArea.style.backgroundColor = `rgba(${color.r}, ${color.g}, ${color.b}, 0.2)`;
          dragArea.style.backdropFilter = 'blur(20px) saturate(180%)';
          dragArea.style.webkitBackdropFilter = 'blur(20px) saturate(180%)';
        }
      } catch (error) {
        console.error('Error getting favicon color:', error);
      }

      // Update URL input and focus
      urlInput.value = webview.getURL();

      // 使用 setTimeout 确保在 DOM 更新后获得焦点和全选
      setTimeout(() => {
        urlInput.focus();
        urlInput.select();
      }, 50);
    }
  });

  // Hide suggestions when clicking outside
  document.addEventListener('click', (e) => {
    if (!suggestionsContainer.contains(e.target) && e.target !== urlInput) {
      hideSuggestions();
    }
  });

  // Add right-click handler to dragArea
  dragArea.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (dragArea.style.display === 'flex') {
      hideDragArea();
    } else {
      // Send message to main process to toggle traffic lights
      showDragArea();
    }
  });

  // 处理 webview 的上下文菜单
  webview.addEventListener('context-menu', (e) => {
    const params = e.params;

    // Check if right click is in top area (30px)
    if (params.y <= 30) {
      e.preventDefault();
      if (dragArea.style.display === 'flex') {
        hideDragArea();
      } else {
        // Send message to main process to toggle traffic lights
        showDragArea();
      }
      return;
    }

    // If right click is on a link, show the context menu
    if (params.linkURL) {
      e.preventDefault();
      ipcRenderer.send('show-context-menu', {
        x: params.x,
        y: params.y,
        linkURL: params.linkURL
      });
      return;
    }

    // For elements without linkURL, we'll try to detect if they are "clickable" and extract potential URLs
    // This is for elements like divs that act as links
    webview.executeJavaScript(`
      (function() {
        try {
          // Get the element at the click coordinates
          const element = document.elementFromPoint(${params.x}, ${params.y});
          if (!element) return null;

          // Check if this element or its parents have click events or other indications of being clickable
          let currentEl = element;
          let checkDepth = 3; // Check up to 3 levels of parent elements

          while (currentEl && checkDepth > 0) {
            // Check for common clickable properties
            const isClickable = (
              currentEl.tagName === 'DIV' && (
                currentEl.getAttribute('role') === 'button' ||
                currentEl.getAttribute('tabindex') === '0' ||
                currentEl.classList.contains('clickable') ||
                currentEl.classList.contains('link') ||
                currentEl.classList.contains('button') ||
                currentEl.classList.contains('history-item') ||
                window.getComputedStyle(currentEl).cursor === 'pointer'
              )
            );

            if (isClickable) {
              // This is likely a clickable element - let's get info about it
              return {
                isClickable: true,
                tagName: currentEl.tagName,
                className: currentEl.className,
                id: currentEl.id,
                text: currentEl.innerText?.trim().substring(0, 50),
                href: currentEl.getAttribute('href') || currentEl.getAttribute('data-href') || currentEl.getAttribute('data-url')
              };
            }

            currentEl = currentEl.parentElement;
            checkDepth--;
          }

          return null;
        } catch (error) {
          console.error('Error detecting clickable elements:', error);
          return null;
        }
      })();
    `).then(result => {
      if (result && result.isClickable) {
        // This appears to be a clickable element without a direct URL
        // We must prevent default to stop the regular context menu
        e.preventDefault();

        // For demonstration purposes, we'll log what we found
        console.log('Detected clickable element:', result);

        // Send to main process to show context menu
        ipcRenderer.send('show-clickable-context-menu', {
          x: params.x,
          y: params.y,
          elementInfo: result
        });
      }
    }).catch(error => {
      console.error('Error executing JavaScript to detect clickable elements:', error);
    });
  });

  // Update drag area color when page loads
  webview.addEventListener('did-finish-load', async () => {
    if (dragArea.style.display === 'flex') {
      try {
        const color = await getFaviconColor();
        // 只有当颜色不是浅色时才更新为带色调的玻璃效果
        if (!(color.r > 240 && color.g > 240 && color.b > 240)) {
          dragArea.style.backgroundColor = `rgba(${color.r}, ${color.g}, ${color.b}, 0.2)`;
          dragArea.style.backdropFilter = 'blur(20px) saturate(180%)';
          dragArea.style.webkitBackdropFilter = 'blur(20px) saturate(180%)';
        }
      } catch (error) {
        console.error('Error getting favicon color:', error);
      }
      
      // 页面加载完成后，立即显示完整的真实URL
      urlInput.value = webview.getURL();
      console.log('did-finish-load: 立即更新URL输入框为最终URL:', webview.getURL());
    }
  });

    // Also update color on navigation within the page
  webview.addEventListener('did-navigate-in-page', async () => {
    if (dragArea.style.display === 'flex') {
      try {
        const color = await getFaviconColor();
        // 只有当颜色不是浅色时才更新为带色调的玻璃效果
        if (!(color.r > 240 && color.g > 240 && color.b > 240)) {
          dragArea.style.backgroundColor = `rgba(${color.r}, ${color.g}, ${color.b}, 0.2)`;
          dragArea.style.backdropFilter = 'blur(20px) saturate(180%)';
          dragArea.style.webkitBackdropFilter = 'blur(20px) saturate(180%)';
        }
      } catch (error) {
        console.error('Error getting favicon color:', error);
      }
      
      // 页面内导航完成后，显示最终的真实URL
      urlInput.value = webview.getURL();
      console.log('did-navigate-in-page: 更新URL输入框为最终URL:', webview.getURL());
    }
  });

  // Update URL when navigation starts
  webview.addEventListener('did-start-loading', () => {
    // 不在这里更新 URL，保持用户输入的 URL
    // 只更新进度条等其他 UI 状态
  });

  // Update URL when navigation completes
  webview.addEventListener('did-navigate', (e) => {
    const currentUrl = e.url;
    console.log('did-navigate: 导航完成，最终URL:', currentUrl);

    // 导航完成后，显示最终的真实URL（可能因重定向而不同）
    if (dragArea.style.display === 'flex') {
      urlInput.value = currentUrl;
      console.log('did-navigate: 更新URL输入框为最终URL:', currentUrl);
    }

    // 更新弹出的 URL 模态框
    const urlModal = document.getElementById('url-modal');
    const modalUrlInput = document.getElementById('url-input');
    if (urlModal.style.display === 'block' && modalUrlInput) {
      modalUrlInput.value = currentUrl;
    }
  });

  // URL update logic now handled by the consolidated did-stop-loading listener above
});

function addToUrlHistory(url) {
  const history = JSON.parse(localStorage.getItem('urlHistory') || '[]');

  // Remove the URL if it already exists to avoid duplicates
  const index = history.indexOf(url);
  if (index > -1) {
    history.splice(index, 1);
  }

  // Add the new URL to the beginning
  history.unshift(url);

  // Keep only the latest MAX_URL_HISTORY entries
  if (history.length > MAX_URL_HISTORY) {
    history.pop();
  }

  localStorage.setItem('urlHistory', JSON.stringify(history));
}

// 添加删除URL历史记录的函数
function removeFromUrlHistory(url) {
  const history = JSON.parse(localStorage.getItem('urlHistory') || '[]');
  const index = history.indexOf(url);
  if (index > -1) {
    history.splice(index, 1);
    localStorage.setItem('urlHistory', JSON.stringify(history));
  }
}

// dragArea中删除历史记录项的函数
function removeDragAreaHistoryItem(url) {
  removeFromUrlHistory(url);
  
  // 直接从当前建议列表中移除该项，而不是重新匹配
  const suggestionsContainer = window.suggestionsContainer;
  if (suggestionsContainer) {
    // 获取当前的建议项
    const suggestionElements = suggestionsContainer.querySelectorAll('.url-suggestion');
    
    // 找到并移除匹配的建议项
    suggestionElements.forEach(element => {
      const elementUrl = element.querySelector('div').textContent;
      if (elementUrl === url) {
        element.remove();
      }
    });
    
    // 重新设置data-index属性，确保索引正确
    const remainingSuggestions = suggestionsContainer.querySelectorAll('.url-suggestion');
    remainingSuggestions.forEach((element, index) => {
      element.setAttribute('data-index', index);
    });
    
    // 重新绑定事件监听器到剩余的建议项
    remainingSuggestions.forEach(suggestion => {
      // 移除旧的事件监听器（如果有的话）
      const newSuggestion = suggestion.cloneNode(true);
      suggestion.parentNode.replaceChild(newSuggestion, suggestion);
      
      // 重新绑定点击事件
      newSuggestion.addEventListener('click', () => {
        const url = window.dragAreaSuggestions[newSuggestion.dataset.index];
        const urlInput = window.urlInput;
        
        console.log('删除建议后点击: 立即显示URL:', url);
        urlInput.value = url;

        // 设置用户输入标记
        window.userInputtedUrl = url;
        window.userInputTime = Date.now();
        
        loadURL(url);
        addToUrlHistory(url);
        hideSuggestions();
        urlInput.blur();
      });

      // 重新绑定悬停事件
      newSuggestion.addEventListener('mouseover', () => {
        window.dragAreaSelectedIndex = parseInt(newSuggestion.dataset.index);
        if (window.dragAreaUpdateSelection) {
          window.dragAreaUpdateSelection();
        }
      });

      newSuggestion.addEventListener('mouseenter', () => {
        const currentIndex = parseInt(newSuggestion.dataset.index);
        if (window.dragAreaSelectedIndex !== currentIndex) {
          newSuggestion.style.background = 'rgba(255, 255, 255, 0.2)';
          newSuggestion.style.transform = 'translateX(1px)';
        }
      });

      newSuggestion.addEventListener('mouseleave', () => {
        const currentIndex = parseInt(newSuggestion.dataset.index);
        if (window.dragAreaSelectedIndex !== currentIndex) {
          newSuggestion.style.background = 'rgba(255, 255, 255, 0.1)';
          newSuggestion.style.transform = 'translateX(0)';
        }
      });
    });
    
    // 如果没有建议项了，隐藏容器
    if (remainingSuggestions.length === 0) {
      suggestionsContainer.style.display = 'none';
    }
    
    // 更新全局suggestions数组（移除被删除的URL）
    if (window.dragAreaSuggestions) {
      const urlIndex = window.dragAreaSuggestions.indexOf(url);
      if (urlIndex > -1) {
        window.dragAreaSuggestions.splice(urlIndex, 1);
      }
    }
    
    // 重置选中索引
    window.dragAreaSelectedIndex = -1;
  }
}

// 模态框中删除历史记录项的函数
function removeModalHistoryItem(url) {
  removeFromUrlHistory(url);
  
  // 触发输入框的input事件以重新生成建议列表
  const input = document.getElementById('url-input');
  if (input) {
    // 触发input事件，这会自动重新生成建议列表
    const inputEvent = new Event('input', { bubbles: true });
    input.dispatchEvent(inputEvent);
  }
}

function fuzzyMatch(pattern, str) {
  pattern = pattern.toLowerCase();
  str = str.toLowerCase();

  let patternIdx = 0;
  let strIdx = 0;

  while (patternIdx < pattern.length && strIdx < str.length) {
    if (pattern[patternIdx] === str[strIdx]) {
      patternIdx++;
    }
    strIdx++;
  }

  return patternIdx === pattern.length;
}

function getUrlSuggestions(input) {
  if (!input) return [];

  const history = JSON.parse(localStorage.getItem('urlHistory') || '[]');
  return history.filter(url => fuzzyMatch(input, url));
}

// close all modal
function closeAllModal() {
  console.log('closeAllModal')
  const urlModal = document.getElementById('url-modal');
  const opacityModal = document.getElementById('opacity-modal');
  const searchModal = document.getElementById('search-modal');
  if (urlModal) urlModal.style.display = 'none';
  if (opacityModal) opacityModal.style.display = 'none';
  if (searchModal) searchModal.style.display = 'none';
}

// 添加导航队列
let navigationQueue = [];
let isNavigating = false;
let currentLoadingUrl = null;

// Setup webview navigation handlers
webview.addEventListener('will-navigate', (e) => {
  isNavigating = true;
  const dragAreaUrlInput = document.querySelector('#drag-area input');
  const modalUrlInput = document.getElementById('url-input');
  const url = e.url;

  // 设置当前正在加载的URL，这样可以准确地与后续的stop-current-navigation请求进行比较
  currentLoadingUrl = url;

  console.log('will-navigate: 即将导航到:', url);

  // 检查是否是用户刚刚输入的URL（5秒内）
  const isUserInput = window.userInputtedUrl &&
                     window.userInputTime &&
                     (Date.now() - window.userInputTime) < 5000;

  if (dragAreaUrlInput && !isEditingUrlInput) {
    if (isUserInput) {
      // 如果是用户刚输入的URL，绝对不要覆盖！
      console.log('will-navigate: !!!!! 用户刚输入URL，拒绝覆盖！保持显示:', window.userInputtedUrl, '拒绝显示:', url);
      // 强制保持用户输入的URL
      dragAreaUrlInput.value = window.userInputtedUrl;

      // 使用setTimeout再次确保不被覆盖
      setTimeout(() => {
        if (dragAreaUrlInput && window.userInputtedUrl) {
          dragAreaUrlInput.value = window.userInputtedUrl;
          console.log('will-navigate: 延迟确保显示用户输入URL:', window.userInputtedUrl);
        }
      }, 10);
    } else {
      // 否则显示目标URL（点击链接等情况）
      dragAreaUrlInput.value = url;
      console.log('will-navigate: 显示目标URL:', url);
    }
  }
  
  if (modalUrlInput && document.getElementById('url-modal').style.display === 'block') {
    modalUrlInput.value = userInputUrl || url;
  }
});

webview.addEventListener('did-navigate', (e) => {
  isNavigating = false;
  processNavigationQueue();
});

webview.addEventListener('did-navigate-in-page', (e) => {
  isNavigating = false;
  processNavigationQueue();
});

webview.addEventListener('did-fail-load', (e) => {
  isNavigating = false;
  processNavigationQueue();
});

// Process navigation queue
function processNavigationQueue() {
  if (navigationQueue.length > 0) {
    const nextNavigation = navigationQueue.shift();
    if (nextNavigation.type === 'url') {
      webview.loadURL(nextNavigation.url);
    } else if (nextNavigation.type === 'back') {
      webview.goBack();
    } else if (nextNavigation.type === 'forward') {
      webview.goForward();
    }
  }
}

// Queue navigation actions
function queueNavigation(type, url = null) {
  // 如果是加载新URL，直接加载而不等待队列
  if (type === 'url' && url) {
    webview.loadURL(url);
    return;
  }

  // 其他导航操作（前进/后退）仍然使用队列
  navigationQueue.push({ type, url });
  if (!isNavigating) {
    processNavigationQueue();
  }
}

// Update loadURL function
function loadURL(url) {
  if (!url) return;

  let processedUrl = url.trim();

  // 如果当前正在加载，先停止加载
  if (isNavigating) {
    console.log('Stopping current navigation before loading new URL');
    webview.stop();
    isNavigating = false;
    currentLoadingUrl = null;
    clearLoadingTimeout();
  }

  console.log('loadURL: 开始处理URL:', processedUrl);

  // 如果是 file:// 协议，直接使用
  if (/^file:\/\//i.test(processedUrl)) {
    webview.loadURL(processedUrl);
    isNavigating = true;
    currentLoadingUrl = processedUrl;
    return;
  }

  // 如果 URL 已经有 http/https 协议
  if (/^https?:\/\//i.test(processedUrl)) {
    webview.loadURL(processedUrl);
    isNavigating = true;
    currentLoadingUrl = processedUrl;
    return;
  }

  // 处理 localhost 或 IP 地址（带端口或不带端口）
  const localhostRegex = /^(localhost|127\.0\.0\.1)(:\d+)?($|\/.*)/i;
  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(:\d+)?($|\/.*)/;

  if (localhostRegex.test(processedUrl) || ipRegex.test(processedUrl)) {
    // 对于本地地址，直接使用 http
    const finalUrl = 'http://' + processedUrl;
    webview.loadURL(finalUrl);
    isNavigating = true;
    currentLoadingUrl = finalUrl;
    return;
  }

  // Split URL into domain and path parts
  const [domainPart, ...pathParts] = processedUrl.split('/');
  const path = pathParts.length > 0 ? '/' + pathParts.join('/') : '';

  // Check if it's a domain with optional port
  const isDomain = /^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*(\.[a-zA-Z]{2,})(:\d+)?$/i.test(domainPart);

  if (isDomain) {
    // 先尝试 HTTPS
    const httpsUrl = 'https://' + processedUrl;

    // 创建一个 HEAD 请求来测试 HTTPS 是否可用
    const xhr = new XMLHttpRequest();
    xhr.open('HEAD', httpsUrl);
    xhr.timeout = 3000; // 3秒超时

    xhr.onload = () => {
      // HTTPS 成功，使用 HTTPS
      webview.loadURL(httpsUrl);
      isNavigating = true;
      currentLoadingUrl = httpsUrl;
    };

    xhr.onerror = () => {
      // HTTPS 失败，回退到 HTTP
      const httpUrl = 'http://' + processedUrl;
      webview.loadURL(httpUrl);
      isNavigating = true;
      currentLoadingUrl = httpUrl;
    };

    xhr.ontimeout = () => {
      // 超时，回退到 HTTP
      const httpUrl = 'http://' + processedUrl;
      webview.loadURL(httpUrl);
      isNavigating = true;
      currentLoadingUrl = httpUrl;
    };

    xhr.send();
    return;
  }

  // If none of the above, treat as a search query
  const searchUrl = 'https://www.google.com/search?q=' + encodeURIComponent(processedUrl);
  webview.loadURL(searchUrl);
  isNavigating = true;
  currentLoadingUrl = searchUrl;
}

// Update back/forward functions
// function goBack() {
//   if (webview.canGoBack()) {
//     queueNavigation('back');
//   }
// }

// function goForward() {
//   if (webview.canGoForward()) {
//     queueNavigation('forward');
//   }
// }

// 修改 new-window 事件处理
webview.addEventListener('new-window', (e) => {
  e.preventDefault();
  const url = e.url;
  console.log('Intercepted new window request:', url, 'Disposition:', e.disposition, 'Features:', e.options?.webPreferences, 'User Gesture:', e.userGesture);

  // 检查是否是由 JavaScript window.open() 带特性调用触发
  const hasWindowFeatures = e.options && (
    e.options.width ||
    e.options.height ||
    e.options.top ||
    e.options.left ||
    (e.options.webPreferences && Object.keys(e.options.webPreferences).length > 0)
  );

  // 检查是否是 JavaScript 脚本触发
  const isJavaScriptInitiated = (
    // window.open() 调用
    e.disposition === 'new-window' ||
    // javascript: 协议
    url.startsWith('javascript:') ||
    // 自动弹窗（非用户手势）
    (e.disposition === 'foreground-tab' && e.userGesture === false)
  );

  // 如果是用户手势触发的新窗口请求（比如点击链接或右键菜单）
  // if (e.userGesture) {
  //   // 如果是javascript:协议，让网站自己处理
  //   if (url.startsWith('javascript:')) {
  //     console.log('JavaScript protocol URL, letting site handle it:', url);
  //     // 恢复默认行为（通过阻止preventDefault）
  //     e.defaultPrevented = false;
  //     return;
  //   }

  //   // 否则，对于普通链接创建新窗口
  //   if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
  //     console.log('User gesture new window request, creating new window:', url);
  //     // 发送消息到主进程创建新窗口，并传递URL
  //     ipcRenderer.send('create-new-window', url);
  //   }
  //   return;
  // }

  // 如果是带窗口特性的 window.open()，让目标网站自行处理
  if (isJavaScriptInitiated && hasWindowFeatures) {
    console.log('Window.open with features, letting target site handle it');
    e.defaultPrevented = false;
    return;
  }

  // 如果是普通的 JavaScript 新窗口/标签页请求，在当前窗口打开
  if (url && (url.startsWith('http://') || url.startsWith('https://'))) {
    console.log('Opening URL in current window:', url);
    
    // 在当前窗口打开URL之前，停止当前的加载
    if (isNavigating) {
      console.log('Stopping current navigation before opening new URL in current window');
      webview.stop();
      isNavigating = false;
      currentLoadingUrl = null;
      clearLoadingTimeout();
      
      // 重置进度条
      const progressBar = document.getElementById('progress-bar');
      if (progressBar) {
        progressBar.style.width = '0%';
      }
    }
    
    webview.loadURL(url);
    isNavigating = true;
    currentLoadingUrl = url;
  } else {
    console.log('Invalid or special protocol URL:', url);
  }
});

// 注入预加载脚本来处理所有链接和按钮点击
// webview.addEventListener('dom-ready', () => {
//   // 不再需要完整的链接处理脚本，因为我们已经在preload.js中处理了早期链接点击
//   // 我们只需添加必要的补充功能或特定情况处理
//   webview.executeJavaScript(`
//     // 添加对可能仍然需要处理的特殊情况的补充
//     // 注意：主要链接点击已在preload.js中处理

//     // 确保特殊情况的弹窗处理
//     if (typeof window.__specialCasesHandled === 'undefined') {
//       window.__specialCasesHandled = true;

//       // 处理特殊的动态添加链接案例（如通过框架添加的链接）
//       setInterval(() => {
//         try {
//           // 查找特殊的动态添加的链接（通常由某些库动态创建）
//           const dynamicLinks = document.querySelectorAll('a[target="_blank"][rel="noopener"]');
//           dynamicLinks.forEach(link => {
//             if (!link.__specialHandled) {
//               link.__specialHandled = true;

//               // 处理特殊的链接
//               if (link.href && (link.href.startsWith('http://') || link.href.startsWith('https://'))) {
//                 link.addEventListener('click', (e) => {
//                   e.preventDefault();
//                   window.postMessage({ type: 'create-new-window', url: link.href }, '*');
//                 });
//               }
//             }
//           });
//         } catch (err) {
//           // 忽略错误
//         }
//       }, 1000);
//     }
//   `);
// });

// 添加消息处理程序来接收 webview 中的消息
window.addEventListener('message', (event) => {
  // 确保消息来源是 webview
  if (event.source === webview.contentWindow) {
    if (event.data.type === 'create-new-window') {
      // 传递消息到主进程
      ipcRenderer.send('create-new-window', event.data.url);
    }
  }
});

// 修改 ipc-message 事件处理中的导航相关部分
webview.addEventListener('ipc-message', (event) => {
  if (event.channel === 'go-back') {
    goBack();
  } else if (event.channel === 'go-forward') {
    goForward();
  } else if (event.channel === 'loadURL') {
    const url = event.args[0];
    
    // 立即显示点击的书签URL
    console.log('!!!!! 点击书签，立即显示URL:', url);
    const dragAreaUrlInput = document.querySelector('#drag-area input');
    if (dragAreaUrlInput) {
      dragAreaUrlInput.value = url;
    }
    
    // 开始导航
    
    loadURL(url);
  } else if (event.channel === 'create-new-window') {
    // 从预加载脚本收到创建新窗口的请求
    ipcRenderer.send('create-new-window', event.args[0]);
  } else if (event.channel === 'stop-current-navigation') {
    // 停止当前导航的请求
    const targetUrl = event.args[0];
    console.log('Received stop-current-navigation request from preload script, target URL:', targetUrl);
    
    // 只有在当前加载的URL与目标URL不同时才停止导航
    // 这样可以避免多次点击同一链接导致停止加载的问题
    if (isNavigating && currentLoadingUrl && targetUrl && currentLoadingUrl !== targetUrl) {
      console.log('Stopping current navigation before loading new URL. Current:', currentLoadingUrl, 'Target:', targetUrl);
      webview.stop();
      isNavigating = false;
      currentLoadingUrl = null;
      clearLoadingTimeout();
      
      // 重置进度条
      const progressBar = document.getElementById('progress-bar');
      if (progressBar) {
        progressBar.style.width = '0%';
      }
      
      console.log('Stopped current navigation for new URL');
    } else if (isNavigating && currentLoadingUrl === targetUrl) {
      console.log('Same URL already loading, ignoring stop request:', targetUrl);
    } else if (!isNavigating) {
      console.log('Not currently navigating');
    }
    
    // 如果提供了目标URL，立即在URL输入框中显示
    if (targetUrl) {
      const dragAreaUrlInput = document.querySelector('#drag-area input');
      if (dragAreaUrlInput) {
        dragAreaUrlInput.value = targetUrl;
        console.log('Immediately displayed target URL in input:', targetUrl);
      }
    }
  } else if (event.channel === 'show-password-save-prompt') {
    // 显示密码保存提示
    const { domain, username, password, isUpdate } = event.args[0];
    showPasswordSavePrompt(domain, username, password, isUpdate);
  } else if (event.channel === 'zoom-wheel') {
    // Command/Ctrl + wheel zoom
    const delta = event.args[0];
    const currentZoom = webview.getZoomFactor();
    const newZoom = Math.min(Math.max(currentZoom + delta, MIN_SCALE), MAX_SCALE);
    webview.setZoomFactor(newZoom);
    console.log('Zoom factor:', newZoom); // Debug log
  } else if (event.channel === 'zoom-gesture') {
    // Pinch/spread zoom
    const scale = event.args[0];
    const currentZoom = webview.getZoomFactor();
    const newZoom = Math.min(Math.max(currentZoom * scale, MIN_SCALE), MAX_SCALE);
    webview.setZoomFactor(newZoom);
    console.log('Zoom factor:', newZoom); // Debug log
  }
});

// Listen for messages from main process
ipcRenderer.on('show-url-modal', () => {
  closeAllModal();
  showUrlModal()
})

ipcRenderer.on('show-opacity-modal', () => {
  closeAllModal();
  showOpacityModal()
})

// 移除重复的go-back和go-forward监听器，已在文件末尾定义

ipcRenderer.on('refresh', () => {
  webview.reload()
})

// Listen for developer tools toggle message
ipcRenderer.on('toggle-devtools', () => {
  webview.openDevTools()
})

// Show current page URL
ipcRenderer.on('show-current-url', () => {
  closeAllModal();
  const modal = document.getElementById('url-modal')
  const input = document.getElementById('url-input')
  const suggestionsList = document.getElementById('url-suggestions')

  // If modal is already shown, close it
  if (modal.style.display === 'block') {
    modal.style.display = 'none'
    input.value = ''
    suggestionsList.style.display = 'none'
    return
  }

  // Otherwise show modal and fill in current URL
  const currentUrl = webview.getURL()
  modal.style.display = 'block'
  input.value = currentUrl
  input.select()
  input.focus()

  // Bind enter key event
  const handleKeydown = (e) => {
    if (e.key === 'Enter') {
      const url = input.value.trim()
      if (url) {
        console.log('另一个URL处理函数: Enter键按下，准备导航到:', url);
        
        // 立即显示URL在主URL输入框中
        const dragAreaUrlInput = document.querySelector('#drag-area input');
        if (dragAreaUrlInput) {
          dragAreaUrlInput.value = url;
          console.log('另一个URL处理函数: 立即在主URL输入框显示:', url);
        }
        
        loadURL(url)
        addToUrlHistory(url)
      }
      modal.style.display = 'none'
      input.value = ''
      suggestionsList.style.display = 'none'
      // Remove event listener
      input.removeEventListener('keydown', handleKeydown)
    } else if (e.key === 'Escape') {
      // 如果正在加载，停止加载
      if (isNavigating) {
        webview.stop();
        isNavigating = false;
        currentLoadingUrl = null;
        clearLoadingTimeout();
      }
      modal.style.display = 'none'
      input.value = ''
      suggestionsList.style.display = 'none'
      // Remove event listener
      input.removeEventListener('keydown', handleKeydown)
    }
  }

  // Add event listener
  input.addEventListener('keydown', handleKeydown)
})

// URL modal
function showUrlModal() {
  const modal = document.getElementById('url-modal');
  const input = document.getElementById('url-input');
  const suggestionsList = document.getElementById('url-suggestions');

  // If modal is already shown, close it
  if (modal.style.display === 'block') {
    modal.style.display = 'none';
    input.value = '';
    return;
  }

  // Otherwise show modal and fill in current URL
  const currentUrl = webview.getURL();
  modal.style.display = 'block';
  input.value = currentUrl;
  input.select();

  modal.style.display = 'block';
  input.focus();

  let selectedIndex = -1;
  let suggestions = [];

  // Handle input changes for suggestions
  input.oninput = () => {
    suggestions = getUrlSuggestions(input.value);
    suggestionsList.innerHTML = suggestions
      .map((url, index) => `
        <div class="url-suggestion" 
             data-index="${index}" 
             onclick="selectSuggestion('${url}')"
             style="display: flex; align-items: center; justify-content: space-between;">
          <span style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${url}</span>
          <button class="delete-history-btn" 
                  data-url="${url.replace(/"/g, '&quot;')}"
                  style="
                    margin-left: 8px;
                    padding: 4px 6px;
                    background: rgba(0, 0, 0, 0.1);
                    border: none;
                    border-radius: 4px;
                    color: #666;
                    cursor: pointer;
                    font-size: 12px;
                    opacity: 0.7;
                    transition: all 0.2s ease;
                    flex-shrink: 0;
                  "
                  onmouseover="this.style.background='rgba(255, 0, 0, 0.2)'; this.style.color='#d32f2f'; this.style.opacity='1';"
                  onmouseout="this.style.background='rgba(0, 0, 0, 0.1)'; this.style.color='#666'; this.style.opacity='0.7';"
                  onclick="event.stopPropagation(); removeModalHistoryItem('${url.replace(/'/g, "\\'")}');">×</button>
        </div>
      `)
      .join('');
    suggestionsList.style.display = suggestions.length ? 'block' : 'none';
    selectedIndex = -1;
  };

  function updateSelection() {
    const items = suggestionsList.querySelectorAll('.url-suggestion');
    items.forEach(item => item.classList.remove('selected'));
    if (selectedIndex >= 0 && selectedIndex < items.length) {
      items[selectedIndex].classList.add('selected');
      items[selectedIndex].scrollIntoView({ block: 'nearest' });
    }
  }

  input.onkeydown = (e) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (suggestions.length > 0) {
        selectedIndex = (selectedIndex + 1) % suggestions.length;
        updateSelection();
        input.value = suggestions[selectedIndex];
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (suggestions.length > 0) {
        selectedIndex = selectedIndex <= 0 ? suggestions.length - 1 : selectedIndex - 1;
        updateSelection();
        input.value = suggestions[selectedIndex];
      }
    } else if (e.key === 'Enter') {
      let url;
      if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
        url = suggestions[selectedIndex];
      } else {
        url = input.value;
      }
      
      console.log('模态框Enter键按下，准备导航到:', url);
      
      // 立即显示URL在主URL输入框中
      const dragAreaUrlInput = document.querySelector('#drag-area input');
      if (dragAreaUrlInput) {
        dragAreaUrlInput.value = url;
        console.log('模态框: 立即在主URL输入框显示:', url);
      }
      
      loadURL(url);
      addToUrlHistory(url);
      modal.style.display = 'none';
      input.value = '';
      suggestionsList.style.display = 'none';
    } else if (e.key === 'Escape') {
      // 如果正在加载，停止加载
      if (isNavigating) {
        webview.stop();
        isNavigating = false;
        currentLoadingUrl = null;
        clearLoadingTimeout();
      }
      modal.style.display = 'none';
      input.value = '';
      suggestionsList.style.display = 'none';
    }
  };
}

// Add this function to handle suggestion selection
window.selectSuggestion = (url) => {
  const input = document.getElementById('url-input');
  input.value = url;
  
  console.log('selectSuggestion: 选择建议URL:', url);
  
  // 立即显示URL在主URL输入框中
  const dragAreaUrlInput = document.querySelector('#drag-area input');
  if (dragAreaUrlInput) {
    dragAreaUrlInput.value = url;
    console.log('selectSuggestion: 立即在主URL输入框显示:', url);
  }
  
  loadURL(url);
  addToUrlHistory(url);
  document.getElementById('url-modal').style.display = 'none';
  document.getElementById('url-suggestions').style.display = 'none';
};

// 添加加载超时处理
let loadingTimeout;
const LOADING_TIMEOUT = 30000; // 30 seconds

function showLoadingErrorToast() {
  const toast = document.createElement('div');
  toast.style.position = 'fixed';
  toast.style.top = '20px';
  toast.style.left = '50%';
  toast.style.transform = 'translateX(-50%)';
  toast.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  toast.style.color = 'white';
  toast.style.padding = '10px 20px';
  toast.style.borderRadius = '5px';
  toast.style.fontSize = '14px';
  toast.style.zIndex = '10000';
  toast.textContent = '无法打开页面';

  document.body.appendChild(toast);

  // 3秒后自动消失
  setTimeout(() => {
    toast.style.transition = 'opacity 0.5s ease-out';
    toast.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 500);
  }, 3000);
}

function startLoadingTimeout(url) {
  // 清除之前的超时计时器
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
  }

  // 设置新的超时计时器
  loadingTimeout = setTimeout(() => {
    console.log('Loading timeout for URL:', url);
    // 显示错误提示
    showLoadingErrorToast();
    // 返回上一页
    webview.goBack();
  }, LOADING_TIMEOUT);
}

function clearLoadingTimeout() {
  if (loadingTimeout) {
    clearTimeout(loadingTimeout);
    loadingTimeout = null;
  }
}

// 监听导航事件
webview.addEventListener('did-start-loading', () => {
  console.log('Loading started, URL:', webview.getURL());
  isNavigating = true;
  navigationStartTime = Date.now();
  currentLoadingUrl = webview.getURL();
});

// Navigation state reset now handled by the consolidated did-stop-loading listener above

webview.addEventListener('did-navigate', (e) => {
  console.log('Did navigate to:', e.url);
});

webview.addEventListener('did-navigate-in-page', (e) => {
  console.log('Did navigate in page to:', e.url);
});

webview.addEventListener('did-fail-load', (e) => {
  console.error('Failed to load:', e.url, 'Error:', e.errorCode, e.errorDescription);
  // 如果加载失败且是当前URL，尝试强制刷新
  if (e.url === webview.getURL()) {
    console.log('Attempting force refresh after load failure');
    ipcRenderer.send('webview-info', { url: e.url });
  }
  isNavigating = false;
  currentLoadingUrl = null;
  clearLoadingTimeout();
});

// Update dock name when page title changes
webview.addEventListener('page-title-updated', (event) => {
  const title = event.title
  if (title) {
    ipcRenderer.send('update-dock-title', title)
  }
})

// Opacity modal
function showOpacityModal() {
  const modal = document.getElementById('opacity-modal')
  const input = document.getElementById('opacity-input')
  modal.style.display = 'block'
  input.focus()

  input.onkeydown = (e) => {
    if (e.key === 'Enter') {
      const opacity = input.value
      if (opacity >= 0.1 && opacity <= 1.0) {
        ipcRenderer.send('set-opacity', opacity)
      }
      modal.style.display = 'none'
      input.value = ''
    }
    if (e.key === 'Escape') {
      modal.style.display = 'none'
      input.value = ''
    }
  }
}

// Listen for navigation events
webview.addEventListener('will-navigate', (e) => {
  console.log('Will navigate:', e.url);
});

// Listen for in-page navigation
webview.addEventListener('did-navigate-in-page', (e) => {
  console.log('Navigated in page:', e.url);
});

// Add functionality to close modal when clicking outside
window.onclick = function(e) {
  const urlModal = document.getElementById('url-modal');
  const opacityModal = document.getElementById('opacity-modal');

  // Handle URL modal
  if (e.target === urlModal) {
    console.log('Closing URL modal');
    urlModal.style.display = 'none';
    const urlInput = document.getElementById('url-input');
    const urlSuggestions = document.getElementById('url-suggestions');
    if (urlInput) urlInput.value = '';
    if (urlSuggestions) urlSuggestions.style.display = 'none';
  }

  // Handle opacity modal
  if (e.target === opacityModal) {
    console.log('Closing opacity modal');
    opacityModal.style.display = 'none';
    const opacityInput = document.getElementById('opacity-input');
    if (opacityInput) opacityInput.value = '';
  }
}

// Prevent modal from closing when clicking inside
document.getElementById('url-modal').addEventListener('mousedown', function(e) {
  e.stopPropagation();
});

document.getElementById('opacity-modal').addEventListener('mousedown', function(e) {
  e.stopPropagation();
});

// Add load-homepage event listener
ipcRenderer.on('load-homepage', async () => {
  closeAllModal();
  const homepagePath = await ipcRenderer.invoke('get-homepage-path');
  // Use file:// protocol to load local files
  webview.loadURL(homepagePath.startsWith('file://') ? homepagePath : 'file://' + homepagePath);
});

// Add zoom-related event listeners
ipcRenderer.on('zoom-in', () => {
  const currentZoom = webview.getZoomFactor();
  webview.setZoomFactor(Math.min(currentZoom + 0.1, 3.0));
});

ipcRenderer.on('zoom-out', () => {
  const currentZoom = webview.getZoomFactor();
  webview.setZoomFactor(Math.max(currentZoom - 0.1, 0.3));
});

ipcRenderer.on('zoom-reset', () => {
  webview.setZoomFactor(1.0);
});

// Add keyboard shortcut listeners
// window.addEventListener('keydown', (e) => {
//   // Command/Ctrl + = zoom in
//   if (e.key === '=' && (e.metaKey || e.ctrlKey)) {
//     e.preventDefault();
//     const currentZoom = webview.getZoomFactor();
//     webview.setZoomFactor(Math.min(currentZoom + 0.1, 3.0));
//   }
//   // Command/Ctrl + - zoom out
//   else if (e.key === '-' && (e.metaKey || e.ctrlKey)) {
//     e.preventDefault();
//     const currentZoom = webview.getZoomFactor();
//     webview.setZoomFactor(Math.max(currentZoom - 0.1, 0.3));
//   }
//   // Command/Ctrl + 0 reset zoom
//   else if (e.key === '0' && (e.metaKey || e.ctrlKey)) {
//     e.preventDefault();
//     webview.setZoomFactor(1.0);
//   }
// });

async function loadAndExecuteUserScripts() {
  try {
    const scripts = await window.userScripts.loadUserScripts();

    for (const script of scripts) {
      try {
        // Use Function constructor to create executable function
        const scriptFunction = new Function(script.content);
        // Execute script
        scriptFunction();
        console.log(`Successfully loaded script: ${script.name}`);
      } catch (error) {
        console.error(`Error executing script ${script.name}:`, error);
      }
    }
  } catch (error) {
    console.error('Error loading user scripts:', error);
  }
}

// Call on program startup
// document.addEventListener('DOMContentLoaded', () => {
//   loadAndExecuteUserScripts();
// });

// Search functionality
let currentSearchIndex = 0;
let totalSearchMatches = 0;

function updateSearchCount() {
  const countElement = document.getElementById('search-count');
  if (totalSearchMatches > 0) {
    countElement.textContent = `${currentSearchIndex + 1}/${totalSearchMatches}`;
  } else {
    countElement.textContent = '未找到';
  }
}

function performSearch(searchText, forward = true) {
  if (!searchText) return;

  webview.findInPage(searchText, {
    forward: forward,
    findNext: true,
    matchCase: false
  });
}

// Listen for find results from webview
webview.addEventListener('found-in-page', (e) => {
  const result = e.result;
  currentSearchIndex = result.activeMatchOrdinal - 1;
  totalSearchMatches = result.matches;
  updateSearchCount();
});

function showSearchModal() {
  const modal = document.getElementById('search-modal');
  const input = document.getElementById('search-input');

  modal.style.display = 'block';
  input.focus();
  input.select();

  // Clear previous search
  webview.stopFindInPage('clearSelection');
  currentSearchIndex = 0;
  totalSearchMatches = 0;
  document.getElementById('search-count').textContent = '';
}

// Search input event handler
document.getElementById('search-input').addEventListener('input', (e) => {
  const searchText = e.target.value;
  if (searchText) {
    performSearch(searchText);
  } else {
    webview.stopFindInPage('clearSelection');
    document.getElementById('search-count').textContent = '';
  }
});

// Search navigation buttons
document.getElementById('search-prev').addEventListener('click', () => {
  const searchText = document.getElementById('search-input').value;
  if (searchText) {
    performSearch(searchText, false);
  }
});

document.getElementById('search-next').addEventListener('click', () => {
  const searchText = document.getElementById('search-input').value;
  if (searchText) {
    performSearch(searchText, true);
  }
});

document.getElementById('search-close').addEventListener('click', () => {
  const modal = document.getElementById('search-modal');
  modal.style.display = 'none';
  webview.stopFindInPage('clearSelection');
});

// Listen for search modal show message
ipcRenderer.on('show-search-modal', () => {
  showSearchModal();
});

// Handle keyboard events for search
document.getElementById('search-input').addEventListener('keydown', (e) => {
  if (e.key === 'Enter') {
    e.preventDefault();
    const searchText = e.target.value;
    if (searchText) {
      performSearch(searchText, !e.shiftKey);
    }
  } else if (e.key === 'Escape') {
    e.preventDefault();
    const modal = document.getElementById('search-modal');
    modal.style.display = 'none';
    webview.stopFindInPage('clearSelection');
  }
});

// 处理加载URL的消息
ipcRenderer.on('load-url', (event, url) => {
  console.log('Received URL to load:', url);
  if (!url) {
    console.error('No URL provided');
    return;
  }

  const webview = document.querySelector('webview');
  if (!webview) {
    console.error('Webview not found');
    return;
  }

  function loadUrlInWebview() {
    console.log('Loading URL in webview:', url);
    try {
      webview.loadURL(url).catch(err => {
        console.error('Failed to load URL:', err);
        webview.src = url;
      });

      // 显示 dragarea 并设置 URL 输入框
      const dragArea = document.getElementById('drag-area');
      const urlInput = dragArea.querySelector('input');

      // 确保 dragarea 可见
      if (dragArea.style.display !== 'flex') {
        // 触发显示 dragarea
        ipcRenderer.send('toggle-traffic-lights');
      }

      // 设置 URL 输入框的值，并在加载完成后聚焦并全选
      setTimeout(() => {
        if (urlInput) {
          urlInput.value = url;
          urlInput.focus();
          urlInput.select();
          //urlInput.readOnly = false;
        }
      }, 100);

    } catch (err) {
      console.error('Failed to load URL:', err);
      webview.src = url;
    }
  }

  // 如果 webview 已经准备好，直接加载
  if (webview.getWebContents) {
    loadUrlInWebview();
  } else {
    // 如果 webview 还没准备好，等待它准备好
    console.log('Waiting for webview to be ready...');
    webview.addEventListener('dom-ready', () => {
      console.log('Webview is ready, loading URL:', url);
      loadUrlInWebview();
    }, { once: true });
  }
});

// Command+Shift key window dragging
const dragOverlay = document.getElementById('drag-overlay');
let isCommandPressed = false;
let isShiftPressed = false;
let lastWindowPosition = { x: 0, y: 0 };

function showDragOverlay() {
  dragOverlay.style.display = 'block';
  // 记录初始窗口位置
  ipcRenderer.invoke('get-window-position').then(position => {
    lastWindowPosition = position;
  });
}

function hideDragOverlay() {
  dragOverlay.style.display = 'none';
}

function updateDragOverlay() {
  if (isCommandPressed && isShiftPressed) {
    showDragOverlay();
  } else {
    hideDragOverlay();
  }
}

// Reset drag state
function resetDragState() {
  isCommandPressed = false;
  isShiftPressed = false;
  hideDragOverlay();
}

// 监听窗口位置变化
ipcRenderer.on('window-moved', (event, position) => {
  if (dragOverlay.style.display === 'block' &&
      (position.x !== lastWindowPosition.x || position.y !== lastWindowPosition.y)) {
    // 如果窗口位置发生变化，说明正在拖动，隐藏遮罩层
    hideDragOverlay();
    resetDragState();
  }
});

// Listen for key state changes from main process
ipcRenderer.on('command-key-change', (event, isPressed) => {
  isCommandPressed = isPressed;
  updateDragOverlay();
});

ipcRenderer.on('shift-key-change', (event, isPressed) => {
  isShiftPressed = isPressed;
  updateDragOverlay();
});

// Listen for reset command from main process
ipcRenderer.on('reset-key-states', resetDragState);

// Also listen for keyboard events in the renderer process as backup
document.addEventListener('keydown', (e) => {
  if (e.key === 'Meta') {
    isCommandPressed = true;
    updateDragOverlay();
  } else if (e.key === 'Shift') {
    isShiftPressed = true;
    updateDragOverlay();
  }
}, true);

document.addEventListener('keyup', (e) => {
  if (e.key === 'Meta') {
    isCommandPressed = false;
    updateDragOverlay();
  } else if (e.key === 'Shift') {
    isShiftPressed = false;
    updateDragOverlay();
  }
}, true);

// Reset state when window loses focus
window.addEventListener('blur', resetDragState, true);

// Reset state when webview gets focus
webview.addEventListener('focus', resetDragState, true);

// Reset state when webview loads new content
webview.addEventListener('did-start-loading', resetDragState);
webview.addEventListener('did-navigate', resetDragState);
webview.addEventListener('did-navigate-in-page', resetDragState);
webview.addEventListener('did-finish-load', resetDragState);

// 监听从主进程传来的URL导航请求
ipcRenderer.on('navigate-to-url', (event, url) => {
  const webview = document.querySelector('webview');
  if (webview) {
    webview.src = url;
  }
});

// Add webview load event listeners for debugging
webview.addEventListener('did-start-loading', () => {
  console.log('Webview: Started loading');
});

// Add render-process-gone handler
webview.addEventListener('render-process-gone', (event) => {
  console.error('Webview render process gone, reason:', event.reason);
  // 尝试重新加载页面
  const currentUrl = webview.getURL();
  webview.loadURL('about:blank').then(() => {
    setTimeout(() => {
      webview.loadURL(currentUrl);
    }, 100);
  });
});

webview.addEventListener('did-finish-load', () => {
  console.log('Webview: Finished loading');
  const currentUrl = webview.getURL();
  console.log('Webview current URL:', currentUrl);
});

webview.addEventListener('did-fail-load', (e) => {
  console.error('Webview: Failed to load:', e.errorCode, e.errorDescription);
});

webview.addEventListener('dom-ready', () => {
  console.log('Webview: DOM is ready');
  // 发送窗口准备就绪的消息
  ipcRenderer.send('window-ready');

  // 设置 URL 输入框焦点
  const dragArea = document.getElementById('drag-area');
  const urlInput = dragArea.querySelector('input');

  // 如果 dragarea 可见，则聚焦并全选 URL 输入框
  if (dragArea.style.display === 'flex' && urlInput) {
    const focusAndSelect = () => {
      urlInput.focus();
      urlInput.select();
      //urlInput.readOnly = false;
    };

    // 立即尝试一次
    focusAndSelect();

    // 在不同的时间点尝试，以确保至少一次成功
    setTimeout(focusAndSelect, 0);
    setTimeout(focusAndSelect, 50);
    setTimeout(focusAndSelect, 100);

    // 使用 requestAnimationFrame 在下一帧也尝试一次
    requestAnimationFrame(() => {
      focusAndSelect();
      // 再次使用 setTimeout 确保在动画帧之后也能执行
      setTimeout(focusAndSelect, 0);
    });
  }
});

// Add bookmark handling functions
async function saveBookmark() {
  try {
    const url = webview.getURL();
    const title = await webview.getTitle();

    console.log('Saving bookmark:', { url, title });
    const result = await ipcRenderer.invoke('save-bookmark', { url, title });

    if (!result.success) {
      if (result.reason === 'already_exists') {
        console.log('Bookmark already exists');
      } else {
        console.error('Failed to save bookmark:', result.message);
      }
    } else {
      console.log('Bookmark saved successfully');
    }
  } catch (error) {
    console.error('Error saving bookmark:', error);
  }
}

// Listen for save-bookmark message
ipcRenderer.on('save-bookmark', () => {
  saveBookmark();
});

// Listen for load-url message (used by bookmarks window)
ipcRenderer.on('load-url', (event, url) => {
  loadURL(url);

  // 确保 dragarea 可见，并聚焦 URL 输入框
  const dragArea = document.getElementById('drag-area');
  const urlInput = dragArea.querySelector('input');

  if (dragArea.style.display === 'flex' && urlInput) {
    // 使用 setTimeout 确保在 DOM 更新后获得焦点和全选
    setTimeout(() => {
      urlInput.focus();
      urlInput.select();
      //urlInput.readOnly = false;
    }, 100);
  }
});

// Handle simulating click on clickable elements and capturing navigation
ipcRenderer.on('simulate-click-and-capture-navigation', (event, data) => {
  console.log('Simulating click and capturing navigation', data);

  // Setup navigation listeners before clicking
  const navigationPromise = new Promise((resolve) => {
    // Create a one-time listener for navigation events
    const handleNavigation = (e) => {
      const url = e.url || webview.getURL();
      console.log('Navigation detected to URL:', url);
      resolve(url);
      cleanup();
    };

    // Create a listener for DOM changes that might indicate a page change without navigation
    let clickTime = Date.now();
    let observing = true;

    // Set up navigation event listeners
    webview.addEventListener('did-start-loading', handleNavigation, { once: true });
    webview.addEventListener('did-navigate', handleNavigation, { once: true });
    webview.addEventListener('did-navigate-in-page', handleNavigation, { once: true });

    // Set up a timeout in case no navigation occurs
    const timeoutId = setTimeout(() => {
      if (observing) {
        console.log('Navigation timeout - capturing current URL');
        resolve(webview.getURL());
        cleanup();
      }
    }, 2000);

    // Cleanup function to remove event listeners
    function cleanup() {
      observing = false;
      webview.removeEventListener('did-start-loading', handleNavigation);
      webview.removeEventListener('did-navigate', handleNavigation);
      webview.removeEventListener('did-navigate-in-page', handleNavigation);
      clearTimeout(timeoutId);
    }
  });

  // Simulate the click after setting up navigation listeners
  webview.executeJavaScript(`
    (function() {
      try {
        // Get the element at the click coordinates
        const element = document.elementFromPoint(${data.x}, ${data.y});
        if (!element) return null;

        // Find the clickable parent if needed
        let clickableElement = element;
        let depth = 3;

        while (clickableElement && depth > 0) {
          // Check if this is likely a clickable element
          if (
            clickableElement.tagName === 'DIV' && (
              clickableElement.getAttribute('role') === 'button' ||
              clickableElement.getAttribute('tabindex') === '0' ||
              clickableElement.classList.contains('clickable') ||
              clickableElement.classList.contains('link') ||
              clickableElement.classList.contains('button') ||
              clickableElement.classList.contains('history-item') ||
              window.getComputedStyle(clickableElement).cursor === 'pointer'
            )
          ) {
            break;
          }

          clickableElement = clickableElement.parentElement;
          depth--;
        }

        if (clickableElement) {
          console.log('Clicking on element:', clickableElement.tagName, clickableElement.className);

          // Track any window.open calls made during this click
          // by temporarily overriding window.open
          let capturedURL = null;
          const originalWindowOpen = window.open;

          window.open = function(url, target, features) {
            // Capture the URL that would be opened
            capturedURL = url;
            console.log('Captured window.open URL:', url);
            // Return a dummy object to prevent errors
            return { closed: true, close: () => {} };
          };

          // Create an anchor tracker for detecting link clicks
          let lastClickedAnchor = null;
          const clickListener = (e) => {
            if (e.target.tagName === 'A' || e.target.closest('a')) {
              lastClickedAnchor = e.target.tagName === 'A' ? e.target : e.target.closest('a');
              e.preventDefault();
            }
          };

          document.addEventListener('click', clickListener, true);

          // Simulate a real click
          const rect = clickableElement.getBoundingClientRect();
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: rect.left + rect.width / 2,
            clientY: rect.top + rect.height / 2
          });

          // Dispatch the click event
          clickableElement.dispatchEvent(clickEvent);

          // Small delay to allow event handlers to execute
          return new Promise(resolve => {
            setTimeout(() => {
              // Clean up event listeners
              document.removeEventListener('click', clickListener, true);

              // Restore original window.open
              window.open = originalWindowOpen;

              // Determine the URL that should be loaded
              let targetUrl = capturedURL;

              // If a link was clicked, get its href
              if (lastClickedAnchor && lastClickedAnchor.href) {
                targetUrl = lastClickedAnchor.href;
              }

              // Return information about the click result
              resolve({
                clicked: true,
                capturedUrl: targetUrl
              });
            }, 200);
          });
        }

        return { clicked: false };
      } catch (error) {
        console.error('Error simulating click:', error);
        return { clicked: false, error: error.message };
      }
    })();
  `).then(async (result) => {
    console.log('Click simulation result:', result);

    if (result && result.clicked) {
      // If we directly captured a URL from window.open or a link click
      if (result.capturedUrl) {
        console.log('Using directly captured URL:', result.capturedUrl);
        ipcRenderer.send('load-captured-url-in-new-window', {
          url: result.capturedUrl,
          newWindowId: data.newWindowId
        });
        return;
      }

      // Wait for navigation or timeout
      const url = await navigationPromise;
      console.log('Navigation captured, target URL:', url);

      // Send the URL to the main process to load in the new window
      ipcRenderer.send('load-captured-url-in-new-window', {
        url,
        newWindowId: data.newWindowId
      });
    } else {
      console.log('Failed to find clickable element');
      ipcRenderer.send('close-empty-window', data.newWindowId);
    }
  }).catch(error => {
    console.error('Error executing click simulation script:', error);
    ipcRenderer.send('close-empty-window', data.newWindowId);
  });
});

// Add color extraction function
async function getFaviconColor() {
  try {
    // Get favicon URL
    const faviconUrl = await webview.executeJavaScript(`
      (function() {
        // Try to get high quality favicon first
        let favicon = document.querySelector('link[rel="icon"][sizes="32x32"], link[rel="icon"][sizes="48x48"], link[rel="icon"][sizes="96x96"]');
        if (!favicon) {
          favicon = document.querySelector('link[rel="icon"], link[rel="shortcut icon"]');
        }
        if (favicon) {
          return favicon.href;
        }
        // Fallback to default favicon location
        return window.location.origin + '/favicon.ico';
      })()
    `);

    // Create an Image element to load the favicon
    const img = new Image();
    img.crossOrigin = 'Anonymous';

    return new Promise((resolve) => {
      img.onload = () => {
        // Create canvas to draw the image
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height).data;

        // Calculate dominant color
        let r = 0, g = 0, b = 0, count = 0;

        // Sample pixels (every 4th pixel)
        for (let i = 0; i < imageData.length; i += 16) {
          // Skip fully transparent pixels
          if (imageData[i + 3] > 0) {
            r += imageData[i];
            g += imageData[i + 1];
            b += imageData[i + 2];
            count++;
          }
        }

        if (count > 0) {
          r = Math.round(r / count);
          g = Math.round(g / count);
          b = Math.round(b / count);

          // Check if color is white or near white (very light)
          const isNearWhite = r > 240 && g > 240 && b > 240;
          if (isNearWhite) {
            // Use light orange as default
            resolve({ r: 255, g: 237, b: 213 });
          } else {
            resolve({ r, g, b });
          }
        } else {
          // Use light orange as default instead of white
          resolve({ r: 255, g: 237, b: 213 });
        }
      };

      img.onerror = () => {
        console.error('Failed to load favicon');
        // Use light orange as default instead of white
        resolve({ r: 255, g: 237, b: 213 });
      };

      // Load the favicon
      img.src = faviconUrl;
    });
  } catch (error) {
    console.error('Error getting favicon color:', error);
    // Use light orange as default instead of white
    return { r: 255, g: 237, b: 213 };
  }
}

// Update titlebar color
async function updateTitlebarColor() {
  const color = await getFaviconColor();
  const titlebar = document.querySelector('.titlebar');
  if (titlebar) {
    titlebar.style.background = `rgba(${color.r}, ${color.g}, ${color.b}, 0.8)`;
  }
}

// Update color when page loads
webview.addEventListener('did-finish-load', async () => {
  console.log('Webview: Finished loading');
  const currentUrl = webview.getURL();
  console.log('Webview current URL:', currentUrl);

  // Update titlebar color if visible
  const titlebar = document.querySelector('.titlebar');
  if (titlebar && titlebar.style.display !== 'none') {
    await updateTitlebarColor();
  }
});

// Also update color on navigation within the page
webview.addEventListener('did-navigate-in-page', async () => {
  const titlebar = document.querySelector('.titlebar');
  if (titlebar && titlebar.style.display !== 'none') {
    await updateTitlebarColor();
  }
});

// Add beforeunload handler to prevent navigation blocking
webview.addEventListener('will-prevent-unload', (event) => {
  console.log('Preventing unload prevention in webview');
  event.preventDefault();
});

// 修改后退处理函数
function goBack() {
  // 如果正在加载，先停止当前加载
  if (isNavigating) {
    console.log('Stopping current navigation before going back');
    webview.stop();
    isNavigating = false;
    currentLoadingUrl = null;
  }

  if (webview.canGoBack()) {
    console.log('Going back immediately');
    webview.goBack();
  }
}

// 修改前进处理函数
function goForward() {
  // 如果正在加载，先停止当前加载
  if (isNavigating) {
    console.log('Stopping current navigation before going forward');
    webview.stop();
    isNavigating = false;
    currentLoadingUrl = null;
  }

  if (webview.canGoForward()) {
    console.log('Going forward immediately');
    webview.goForward();
  }
}

// 修改主进程消息处理
ipcRenderer.on('go-back', () => {
  goBack();
});

ipcRenderer.on('go-forward', () => {
  goForward();
});

// Add force refresh handler
ipcRenderer.on('get-webview-info', () => {
  const url = webview.getURL();
  ipcRenderer.send('webview-info', { url });
});

// Handle force refresh complete
ipcRenderer.on('force-refresh-complete', () => {
  console.log('Force refresh completed');
  isNavigating = false;
  currentLoadingUrl = null;
  clearLoadingTimeout();
});

// Handle force refresh fallback
ipcRenderer.on('force-refresh-fallback', () => {
  console.log('Using fallback method for force refresh');
  // 停止当前加载
  webview.stop();

  // 清除所有事件监听器
  webview.executeJavaScript(`
    (function() {
      try {
        // 移除所有事件监听器
        const oldElement = document.documentElement;
        const newElement = oldElement.cloneNode(true);
        oldElement.parentNode.replaceChild(newElement, oldElement);

        // 清除beforeunload
        window.onbeforeunload = null;
        window.onunload = null;

        // 强制刷新
        window.location.reload(true);
      } catch (error) {
        console.error('Error in fallback refresh:', error);
      }
    })();
  `).catch(() => {
    // 如果JavaScript执行失败，使用最后的备用方案
    const currentUrl = webview.getURL();
    webview.loadURL('about:blank').then(() => {
      setTimeout(() => {
        webview.loadURL(currentUrl);
      }, 100);
    });
  });
});

// Handle force navigate fallback
ipcRenderer.on('force-navigate-fallback', (event, url) => {
  console.log('Using fallback method for force navigation to:', url);
  // 停止当前加载
  webview.stop();

  // 先加载空白页面，然后加载目标URL
  webview.loadURL('about:blank').then(() => {
    setTimeout(() => {
      webview.loadURL(url);
    }, 100);
  }).catch(() => {
    // 如果还是失败，直接尝试加载目标URL
    webview.loadURL(url);
  });
});

// Add navigation start time tracking
let navigationStartTime = 0;

// Navigation start tracking - did-stop-loading handler consolidated above
webview.addEventListener('did-start-loading', () => {
  console.log('Loading started, URL:', webview.getURL());
  isNavigating = true;
  navigationStartTime = Date.now();
  currentLoadingUrl = webview.getURL();
});

// Navigation stop logic now handled by the consolidated did-stop-loading listener above

// 添加 load-commit 事件处理器来确保 URL 输入框获得焦点
// webview.addEventListener('load-commit', () => {
//   const dragArea = document.getElementById('drag-area');
//   const urlInput = dragArea.querySelector('input');
//   if (dragArea.style.display === 'flex' && urlInput) {
//     const focusAndSelect = () => {
//       urlInput.focus();
//       urlInput.select();
//     };

//     // 立即尝试一次
//     focusAndSelect();

//     // 在不同的时间点尝试，以确保至少一次成功
//     setTimeout(focusAndSelect, 0);
//     setTimeout(focusAndSelect, 50);
//     setTimeout(focusAndSelect, 100);

//     // 使用 requestAnimationFrame 在下一帧也尝试一次
//     requestAnimationFrame(() => {
//       focusAndSelect();
//       // 再次使用 setTimeout 确保在动画帧之后也能执行
//       setTimeout(focusAndSelect, 0);
//     });
//   }
// });

// Add IPC handler for Escape key
ipcRenderer.on('handle-escape', () => {
  // 如果正在加载，停止加载
  if (isNavigating) {
    webview.stop();
    isNavigating = false;
    currentLoadingUrl = null;
    clearLoadingTimeout();
  }

  // 隐藏所有建议容器
  const containers = [
    document.querySelector('#drag-area + div'), // 动态创建的建议容器
    document.getElementById('url-suggestions')  // URL模态框中的建议容器
  ];

  containers.forEach(container => {
    if (container) {
      container.style.display = 'none';
    }
  });

  // 关闭所有模态框
  closeAllModal();

  // 获取 URL 输入框
  const dragArea = document.getElementById('drag-area');
  const urlInput = dragArea.querySelector('input');

  if (urlInput) {
    // 使 URL 输入框失焦
    urlInput.blur();

    // 保持当前输入框中的内容不变
    // 不再更新 URL 输入框的值
  }
});

// 全局点击事件，直接用window.suggestionsContainer和window.urlInput
// 放在DOMContentLoaded外部
document.addEventListener('click', function(e) {
  if (
    window.suggestionsContainer &&
    window.suggestionsContainer.style.display === 'block' &&
    e.target !== window.urlInput &&
    !window.suggestionsContainer.contains(e.target)
  ) {
    hideSuggestions();
  }
});

// 封装显示/隐藏建议列表的函数
function showSuggestions() {
  window.suggestionsContainer.style.display = 'block';
  if (window.webview) window.webview.style.pointerEvents = 'none';
}
function hideSuggestions() {
  window.suggestionsContainer.style.display = 'none';
  if (window.webview) window.webview.style.pointerEvents = 'auto';
}

// 密码保存提示框函数
let currentPasswordPrompt = null;

function showPasswordSavePrompt(domain, username, password, isUpdate = false) {
  // 移除现有的提示框
  if (currentPasswordPrompt) {
    currentPasswordPrompt.remove();
    currentPasswordPrompt = null;
  }
  
  const prompt = document.createElement('div');
  prompt.className = 'password-save-prompt';
  prompt.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    max-width: 320px;
    animation: slideIn 0.3s ease-out;
  `;
  
  prompt.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
      <div style="color: #333; font-weight: 500;">
        ${isUpdate ? '更新密码' : '保存密码'}？
      </div>
      <button class="close-password-btn" style="
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 18px;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
      " title="关闭">×</button>
    </div>
    <div style="margin-bottom: 12px; color: #666; font-size: 12px;">
      网站：${domain}<br>
      用户名：${username}
    </div>
    <div style="display: flex; gap: 8px;">
             <button class="save-password-btn" style="
         flex: 1;
         background: #007AFF;
         color: white;
         border: none;
         border-radius: 4px;
         padding: 8px 12px;
         cursor: pointer;
         font-size: 12px;
       ">${isUpdate ? '更新' : '保存'}</button>
       <button class="cancel-password-btn" style="
         flex: 1;
         background: #f0f0f0;
         color: #333;
         border: none;
         border-radius: 4px;
         padding: 8px 12px;
         cursor: pointer;
         font-size: 12px;
       ">取消</button>
     </div>
   `;
   
   // 添加动画样式（如果还没有的话）
   if (!document.querySelector('#password-prompt-style')) {
     const style = document.createElement('style');
     style.id = 'password-prompt-style';
     style.textContent = `
       @keyframes slideIn {
         from { transform: translateX(100%); opacity: 0; }
         to { transform: translateX(0); opacity: 1; }
       }
       .close-password-btn:hover {
         background-color: #f0f0f0 !important;
         color: #333 !important;
       }
     `;
     document.head.appendChild(style);
   }
   
   document.body.appendChild(prompt);
   currentPasswordPrompt = prompt;
   
   // 绑定事件
   prompt.querySelector('.save-password-btn').addEventListener('click', async () => {
     try {
       const result = await ipcRenderer.invoke('save-password', domain, username, password);
       if (result.success) {
         prompt.remove();
         currentPasswordPrompt = null;
         showPasswordSavedToast();
       }
     } catch (error) {
       console.error('Error saving password:', error);
     }
   });
   
   prompt.querySelector('.cancel-password-btn').addEventListener('click', () => {
     prompt.remove();
     currentPasswordPrompt = null;
   });

   prompt.querySelector('.close-password-btn').addEventListener('click', () => {
     prompt.remove();
     currentPasswordPrompt = null;
   });
 }

 function showPasswordSavedToast() {
   const toast = document.createElement('div');
   toast.style.cssText = `
     position: fixed;
     top: 20px;
     right: 20px;
     background: #4CAF50;
     color: white;
     padding: 12px 16px;
     border-radius: 6px;
     font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
     font-size: 14px;
     z-index: 999999;
     animation: slideIn 0.3s ease-out;
   `;
   toast.textContent = '密码已保存';
   
   document.body.appendChild(toast);
   
   setTimeout(() => {
     if (toast.parentNode) toast.remove();
   }, 3000);
 }
